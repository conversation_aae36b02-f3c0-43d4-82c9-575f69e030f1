# Use an official Python runtime as the base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies for OpenCV and OpenGL
RUN apt-get update && apt-get install -y \
    libgl1 \
    libglx-mesa0 \
    libglib2.0-0 \
    libjpeg-dev \
    zlib1g-dev \
    gcc \
    g++ \
    curl \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy application code
COPY face_landmarker_v2_with_blendshapes.task .
COPY resnet18.onnx .
COPY imagenet_classes.txt .
COPY requirements.txt .
COPY ml_models.py .
COPY gemini_api.py .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Expose port
EXPOSE 8076

# Command to run the FastAPI app
CMD ["uvicorn", "gemini_api:app", "--host", "0.0.0.0", "--port", "8076"]