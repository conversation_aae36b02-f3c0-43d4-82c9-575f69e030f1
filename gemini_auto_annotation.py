import os
import json
import time
import logging
import base64
from pathlib import Path
from dotenv import load_dotenv
from google import genai
from google.genai import types
from PIL import Image
from pydantic import BaseModel, ValidationError, Field
from typing import List, Optional, Literal

load_dotenv()

# Configure logging for token usage tracking
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Step 1: Initialize API client
model_name = "gemini-2.5-pro"
client = genai.Client()

# Configuration flags
USE_EXAMPLES_AND_CACHING = False
MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))  # Configurable max retries from .env

# Step 2: Prompt for face analysis
base_prompt = """
You are an expert dermatologist and facial analysis AI. Analyze the provided human face image step by step. First, evaluate overall image quality: if blurry, unclear (due to brightness, contrast, or lighting), not a human face (others), or a side_face, report that immediately and stop. If the image is clear and shows a frontal human face, proceed with comprehensive analysis.

CLASSIFY ETHNICITY using these definitions:
- ethnicity_northern: Facial features typically associated with Northern Indian origin
- ethnicity_southern: Facial features typically associated with Southern Indian origin  
- ethnicity_north_eastern: Facial features typically associated with North Eastern Indian origin
- ethnicity_others: Facial features that do not clearly fall into the above three categories

CLASSIFY DEMOGRAPHICS:
- Gender: male or female
- Age group: 15-20, 20-25, 25-30, 30-35, 35-40, 40-45, 45-50, 50-55, 55-60, 60-65, 65-70, 70-75, 75-80

ANALYZE ALL SKIN CONDITIONS using these detailed definitions:
- excessive_oiliness: The skin has a noticeable, reflective shine (especially on the forehead, nose, and chin), may look slick or greasy, and often shows visible light reflections or glare in photos.
- excessive_wrinkles: Deep, well-defined creases or lines are clearly visible on the face (such as across the forehead, around the eyes, or mouth), often forming furrows or folds, most prominent in older individuals.
- slight_wrinkles: Fine lines or minor wrinkles that are visible on the face, commonly appearing around the eyes, mouth, or forehead of middle-aged individuals. These lines are subtle but noticeable, and do not form deep creases.
- less_acne: Mild acne characterized by a few small red or flesh-colored bumps (papules) or whiteheads, typically scattered sparsely across the face without widespread inflammation or cysts.
- excessive_acne: Severe acne with numerous inflamed red bumps, pustules, nodules, or cysts densely clustered on the face, often accompanied by swelling, pus, or large areas of irritation.
- scar_due_to_acne: Noticeable indentations, pits, raised scars, or discolored marks on the skin, usually irregularly shaped, in places where previous acne lesions have healed.
- pimple: A clearly defined, round, red, swollen bump on the skin, often with a visible white or yellow center (pus), usually standing out from the surrounding area.
- hyper_pigmentation: Distinct, well-defined patches or spots of skin that are visibly darker than the person’s natural skin tone, often brown, black, or grayish in color, and not caused by shadows or lighting.
- uneven_pigmentation: The overall skin tone appears inconsistent, with irregular areas of lighter and darker color distributed across the face, not limited to isolated spots but affecting larger zones or blending gradually.
- spots: Small, localized marks on the skin that differ in color or texture from the surrounding area, including sunspots, freckles, or age spots, usually round or oval and lighter or darker than the base skin tone.
- small_skin_pores: The facial skin surface appears smooth and refined, with pores that are barely visible even on close inspection, showing no prominent openings or irregular texture.
- big_skin_pores: Noticeably enlarged, round or oval-shaped openings on the facial skin, often concentrated around the nose, cheeks, or forehead, appearing as small visible dots or pits that create a rough or uneven skin texture.
- under_eye_bag: Visible, rounded swelling or puffiness beneath the lower eyelids, presenting as bulging, saggy, or shadowed areas that protrude from the natural contour of the face, sometimes with darker coloration.
- droopy_eyelid: The upper eyelid visibly sags or folds downward over the eye, partially covering the upper part of the iris or eyelashes, resulting in a heavy-lidded or tired appearance, often more pronounced on one or both eyes.
- redness: Clearly demarcated or diffuse regions of the facial skin that appear pink, red, or flushed compared to the surrounding areas, often affecting the cheeks, nose, or chin, and not attributable to lighting or makeup.
- finelines: Very thin, shallow lines or creases visible on the skin, especially around the eyes (crow’s feet), mouth, or forehead, appearing as faint but distinct marks that do not deeply indent the skin surface.
- eczema: Patches of skin displaying dryness, scaling, and redness, often with rough or cracked texture; may show small bumps, flakiness, or areas that look irritated or inflamed, typically distinct from the surrounding healthy skin.
- psoriasis: Clearly defined, raised plaques or patches on the skin that are red and covered with silvery-white scales, often appearing thickened and slightly elevated above the surrounding skin, with sharp borders.
- rosacea: Persistent or recurrent areas of facial redness, flushing, or visible blood vessels (telangiectasia), especially on the cheeks and nose, sometimes accompanied by small red bumps or swelling, distinct from temporary blushing.

ANALYSIS PROCESS:
1. Look at overall image quality and confirm if analysis can proceed. If image is unclear, say so.
2. Identify gender, ethnicity, and age_group with detailed reasoning
3. Systematically examine each facial area (forehead, nose, eyes, cheeks, mouth, chin, overall face)
4. For each area, check for ALL relevant skin conditions from the list above
5. For each detected condition, provide:
   - Specific visual evidence (texture, color, shape, distribution)
   - Detailed reasoning for the classification
   - Confidence score (0-100) based on image clarity and visibility
   - Exact location description
6. Only include conditions that are actually present; omit absent ones
7. Be thorough - examine every condition systematically

Output ONLY a JSON object like this:
{
  "quality_issues": ["blurry", "unclear", "others", "side_face"] or [] if none,
  "gender": "male" or "female",
  "ethnicity": "ethnicity_northern" or "ethnicity_southern" or "ethnicity_north_eastern" or "ethnicity_others",
  "age_group": "15-20" or etc.,
  "skin_conditions": [
    {
      "condition": "excessive_acne",
      "location": "forehead and left cheek",
      "description": "Multiple red inflamed pustules and papules clustered on forehead with 3-4 visible on left cheek. Clear inflammatory response with surrounding erythema. Consistent with moderate to severe acne presentation.",
      "confidence": 85
    },
    {
      "condition": "hyper_pigmentation",
      "location": "right cheek", 
      "description": "Dark brown patches approximately 1-2cm in diameter on right cheek area, clearly darker than surrounding skin tone. Appears to be post-inflammatory hyperpigmentation possibly from previous acne.",
      "confidence": 78
    }
  ]
}
"""

# Pydantic models for response validation
class SkinCondition(BaseModel):
    condition: str = Field(..., description="The skin condition name")
    location: str = Field(..., description="Location of the condition on the face")
    description: str = Field(..., description="Detailed description of the condition")
    confidence: int = Field(..., ge=0, le=100, description="Confidence score between 0-100")

class FaceAnalysisResponse(BaseModel):
    quality_issues: List[Literal["blurry", "unclear", "others", "side_face"]] = Field(default_factory=list)
    gender: Optional[Literal["male", "female"]] = Field(None, description="Gender classification")
    ethnicity: Optional[Literal["ethnicity_northern", "ethnicity_southern", "ethnicity_north_eastern", "ethnicity_others"]] = Field(None, description="Ethnicity classification")
    age_group: Optional[Literal["15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80"]] = Field(None, description="Age group classification")
    skin_conditions: List[SkinCondition] = Field(default_factory=list, description="List of detected skin conditions")

# Initialize variables for examples and cache
uploaded_examples = {}
cache = None

if USE_EXAMPLES_AND_CACHING:
    logger.info("Loading example images for caching...")
    example_images = {
        # "ethnicity_northern": "examples/ethnicity_northern_example.jpg",
        # "ethnicity_southern": "examples/ethnicity_southern_example.jpg", 
        # "ethnicity_north_eastern": "examples/ethnicity_north_eastern_example.jpg",
        # "ethnicity_others": "examples/ethnicity_others_example.jpg",
        
        "excessive_oiliness": "examples/excessive_oiliness_example.jpg",
        "excessive_wrinkles": "examples/excessive_wrinkles_example.jpg",
        "slight_wrinkles": "examples/slight_wrinkles_example.jpg",
        # "less_acne": "examples/less_acne_example.jpg",
        # "excessive_acne": "examples/excessive_acne_example.jpg",
        "scar_due_to_acne": "examples/scar_due_to_acne_example.jpg",
        # "pimple": "examples/pimple_example.jpg",
        "hyper_pigmentation": "examples/hyper_pigmentation_example.jpg",
        "uneven_pigmentation": "examples/uneven_pigmentation_example.jpg",
        "spots": "examples/spots_example.jpg",
        "small_skin_pores": "examples/small_skin_pores_example.jpg",
        "big_skin_pores": "examples/big_skin_pores_example.jpg",
        "under_eye_bag": "examples/under_eye_bag_example.jpg",
        "droopy_eyelid": "examples/droopy_eyelid_example.jpg",
        "redness": "examples/redness_example.jpg",
        "finelines": "examples/finelines_example.jpg",
        "eczema": "examples/eczema_example.jpg",
        "psoriasis": "examples/psoriasis_example.jpg",
        "rosacea": "examples/rosacea_example.jpg"
    }
    
    # Upload example images for caching
    for condition, path in example_images.items():
        with open(path, "rb") as f:
            uploaded_examples[condition] = client.files.upload(
                file=f.read(),
                config=types.UploadFileConfig(
                    display_name=f"example-{condition}",
                    mime_type="image/jpeg"
                )
            )
else:
    logger.info("Running without examples and caching (USE_EXAMPLES_AND_CACHING=False)")

# Create cache only if using examples
if USE_EXAMPLES_AND_CACHING:
    cache_contents = [base_prompt \
            + "I'll attach some example images on how some skin conditions look like for male/female. You can learn from those and that might help you in giving answer. \n"] \
            + list(uploaded_examples.values())  
            
    cache = client.caches.create(
        model=model_name,
        config=types.CreateCachedContentConfig(
            contents=cache_contents,
            ttl_seconds=3600  # 1 hour TTL
        )
    )
    print(f"Cache created: {cache.name}")
    logger.info(f"Created cache: {cache.name} with {len(cache_contents)} items")
else:
    logger.info("Skipping cache creation (USE_EXAMPLES_AND_CACHING=False)")

def validate_and_retry_response(contents, img_name, max_retries=MAX_RETRIES):
    """
    Validate GenAI response with Pydantic and retry on validation failure.

    Args:
        contents: List of content parts for generation (base_prompt, image, instruction)
        img_name: Name of the image being processed
        max_retries: Maximum number of retries (default from config)

    Returns:
        tuple: (success: bool, result: dict, error_message: str)
    """
    original_instruction = contents[-1] if isinstance(contents[-1], str) else "Please classify the image into all mentioned categories."

    for attempt in range(max_retries + 1):  # +1 for initial attempt
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries + 1} for {img_name}")

            # Generate content from model
            generation_config = {}
            if USE_EXAMPLES_AND_CACHING and cache:
                generation_config["cached_content"] = cache.name

            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=types.GenerateContentConfig(**generation_config)
            )

            response_text = response.candidates[0].content.parts[0].text
            logger.info(f"Raw response for {img_name} (attempt {attempt + 1}): {response_text[:200]}...")

            # Clean the response text (remove markdown formatting if present)
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:].strip()
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3].strip()

            # Parse JSON
            try:
                json_data = json.loads(cleaned_text)
            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error for {img_name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    # Reset to original instruction and add retry guidance
                    contents[-1] = original_instruction + f"\n\nIMPORTANT: Your previous response had JSON parsing errors: {str(e)}. Please ensure your response is ONLY a valid JSON object with no additional text or markdown formatting."
                    continue
                else:
                    return False, {"error": "json_decode_failed", "raw_response": response_text}, f"Failed to decode JSON after {max_retries + 1} attempts"

            # Validate with Pydantic
            try:
                validated_response = FaceAnalysisResponse(**json_data)
                logger.info(f"✓ Validation successful for {img_name} on attempt {attempt + 1}")
                return True, validated_response.model_dump(), None

            except ValidationError as e:
                logger.warning(f"Pydantic validation error for {img_name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    # Reset to original instruction and add specific validation guidance
                    validation_errors = str(e)
                    contents[-1] = original_instruction + f"\n\nIMPORTANT: Your previous response had validation errors: {validation_errors}. Please ensure your JSON response strictly follows the required format with all required fields and correct data types."
                    continue
                else:
                    return False, {"error": "validation_failed", "validation_errors": str(e), "raw_response": response_text}, f"Validation failed after {max_retries + 1} attempts"

        except Exception as e:
            logger.error(f"Unexpected error for {img_name} (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                # Reset to original instruction for retry
                contents[-1] = original_instruction + f"\n\nIMPORTANT: Previous attempt failed with error: {str(e)}. Please provide a valid JSON response."
                continue
            else:
                return False, {"error": "generation_failed", "details": str(e)}, f"Generation failed after {max_retries + 1} attempts"

    return False, {"error": "max_retries_exceeded"}, f"Exceeded maximum retries ({max_retries + 1})"

# Helper function to log token usage
def log_token_usage(response, image_name="", operation="generate_content"):
    """Log token usage information from API response"""
    if hasattr(response, 'usage_metadata'):
        usage = response.usage_metadata
        total_tokens = getattr(usage, 'total_token_count', 0)
        prompt_tokens = getattr(usage, 'prompt_token_count', 0)
        candidates_tokens = getattr(usage, 'candidates_token_count', 0)
        # Handle cases where cached_content_token_count might be None
        cached_tokens = getattr(usage, 'cached_content_token_count', 0) or 0
        
        logger.info(f"Token Usage [{operation}] {image_name}:")
        logger.info(f"  Total: {total_tokens} | Prompt: {prompt_tokens} | Response: {candidates_tokens} | Cached: {cached_tokens}")
        
        # Calculate cost efficiency if cached tokens are used
        if cached_tokens > 0:
            cache_efficiency = (cached_tokens / total_tokens) * 100 if total_tokens > 0 else 0
            logger.info(f"  Cache Efficiency: {cache_efficiency:.1f}% ({cached_tokens}/{total_tokens} tokens from cache)")
        
        return {
            'total_tokens': total_tokens,
            'prompt_tokens': prompt_tokens,
            'candidates_tokens': candidates_tokens,
            'cached_tokens': cached_tokens
        }
    else:
        logger.warning(f"No usage metadata available for {operation} {image_name}")
        return None

def discover_images(folder_path, supported_extensions=('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')):
    """Discover all image files in a folder recursively"""
    folder = Path(folder_path)
    if not folder.exists():
        raise ValueError(f"Folder {folder_path} does not exist")
    
    image_files = []
    for ext in supported_extensions:
        # Search recursively for images with each extension
        image_files.extend(folder.rglob(f"*{ext}"))
        image_files.extend(folder.rglob(f"*{ext.upper()}"))  # Also check uppercase
    
    # Convert to strings and sort for consistent ordering
    image_paths = [str(img) for img in image_files]
    image_paths.sort()
    
    logger.info(f"Discovered {len(image_paths)} images in {folder_path}")
    return image_paths

def create_batches(image_paths, batch_size=100):
    """Split image paths into batches of specified size"""
    batches = []
    for i in range(0, len(image_paths), batch_size):
        batch = image_paths[i:i + batch_size]
        batches.append(batch)
    
    logger.info(f"Created {len(batches)} batches of up to {batch_size} images each")
    return batches

def process_folder_in_batches(folder_path, batch_size=100, output_dir="batch_results"):
    """Process all images in a folder using batch processing"""
    # Discover all images in the folder
    image_paths = discover_images(folder_path)
    
    if not image_paths:
        logger.warning(f"No images found in {folder_path}")
        return
    
    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Split into batches
    batches = create_batches(image_paths, batch_size)
    
    # Process each batch
    all_results = {}
    for i, batch in enumerate(batches, 1):
        logger.info(f"\n=== Processing Batch {i}/{len(batches)} ({len(batch)} images) ===")
        
        batch_output_file = output_path / f"batch_{i:03d}_results.json"
        
        # Process this batch
        batch_results = annotate_images(
            image_paths=batch, 
            use_batch=True, 
            output_file=str(batch_output_file)
        )
        
        # Merge results
        if batch_results:
            all_results.update(batch_results)
        
        logger.info(f"Batch {i} completed. Results saved to {batch_output_file}")
    
    # Save combined results
    combined_output = output_path / "all_results_combined.json"
    with open(combined_output, "w") as f:
        json.dump(all_results, f, indent=4)
    
    logger.info(f"\n=== FOLDER PROCESSING COMPLETE ===")
    logger.info(f"Total images processed: {len(image_paths)}")
    logger.info(f"Total batches: {len(batches)}")
    logger.info(f"Combined results saved to: {combined_output}")
    logger.info(f"Individual batch results in: {output_dir}/")
    
    return all_results

def annotate_images(image_paths, use_batch=False, output_file="annotations.json"):
    results = {}
    logger.info(f"Starting annotation of {len(image_paths)} images (batch_mode={use_batch})")

    if use_batch:
        # Prepare batch JSONL content
        batch_requests = []
        for img_path in image_paths:
            img_name = os.path.basename(img_path)
            # Upload file using file path
            uploaded_img = client.files.upload(file=img_path)

            # CORRECTED request structure
            request_content = {
                "contents": [
                    {
                        "parts": [
                            {"file_data":{"file_uri": uploaded_img.uri, "mime_type": uploaded_img.mime_type}},
                            {"text": base_prompt + "Please classify the image into all mentioned categories."}
                        ]
                    }
                ]
            }

            # Add cache reference only if cache exists
            if USE_EXAMPLES_AND_CACHING and cache:
                request_content["cached_content"] = cache.name

            request = {
                "key": img_name,
                "request": request_content
            }
            batch_requests.append(request)

        # Write to temp JSONL file
        with open("batch_requests.jsonl", "w") as f:
            for req in batch_requests:
                f.write(json.dumps(req) + "\n")

        # Upload JSONL and create batch job
        uploaded_batch = client.files.upload(
            file="batch_requests.jsonl",
            config=types.UploadFileConfig(
                display_name="face-annotation-batch-requests",
                mime_type="jsonl"
            )
        )
        batch_job = client.batches.create(
            model=model_name,
            src=uploaded_batch.name,
            config={"display_name": "Face Annotation Batch"}
        )
        print(f"Batch job created: {batch_job.name}")

        # --- Start of Robust Polling and Result Handling ---
        poll_interval = 60  # Start with 60 seconds
        max_poll_interval = 600  # Max 10 minutes
        max_polls = 20  # ~30 minutes timeout
        
        original_batch_name = batch_job.name
        final_batch_job = None

        for poll_count in range(1, max_polls + 1):
            try:
                # Correctly poll using client.batches.get(name=...) as per documentation
                final_batch_job = client.batches.get(name=original_batch_name)
                
                print(f"Poll #{poll_count}/{max_polls}: Batch job state: {final_batch_job.state.name}")
                
                # Check against the correct state names from the SDK
                if final_batch_job.state.name in ('JOB_STATE_SUCCEEDED', 'JOB_STATE_FAILED', 'JOB_STATE_CANCELLED'):
                    break  # Exit loop on terminal state

            except Exception as e:
                # This can happen if the job is not yet retrievable. Treat as a non-fatal polling error.
                print(f"Poll #{poll_count}/{max_polls}: Error polling job status: {e}. Retrying in {poll_interval}s...")
            
            # Exponential backoff
            if poll_count > 3:
                poll_interval = min(poll_interval * 1.5, max_poll_interval)
            
            time.sleep(poll_interval)

        # --- Result Processing ---
        if final_batch_job is None:
            print("Error: Polling timed out. Batch job was never found or consistently failed to retrieve.")
        
        elif final_batch_job.state.name == 'JOB_STATE_SUCCEEDED':
            print("Batch job SUCCEEDED. Processing results...")
            try:
                if hasattr(final_batch_job, 'dest') and final_batch_job.dest and hasattr(final_batch_job.dest, 'file_name') and final_batch_job.dest.file_name:
                    output_file_name = final_batch_job.dest.file_name
                    # Correctly download the file using the 'name' keyword argument
                    result_file = client.files.download(file=output_file_name)
                    
                    for line_num, line in enumerate(result_file.decode("utf-8").splitlines(), 1):
                        if not line.strip(): continue
                        try:
                            data = json.loads(line)
                            img_name = data.get("key", f"unknown_image_{line_num}")
                            
                            if "response" in data and "candidates" in data["response"]:
                                try:
                                    response_text = data["response"]["candidates"][0]["content"]["parts"][0]["text"]

                                    # Clean the response text
                                    cleaned_text = response_text.strip()
                                    if cleaned_text.startswith("```json"):
                                        cleaned_text = cleaned_text[7:].strip()
                                    if cleaned_text.endswith("```"):
                                        cleaned_text = cleaned_text[:-3].strip()

                                    # Try to parse and validate JSON
                                    try:
                                        json_data = json.loads(cleaned_text)
                                        # Validate with Pydantic
                                        try:
                                            validated_response = FaceAnalysisResponse(**json_data)
                                            results[img_name] = validated_response.model_dump()
                                            print(f"  ✓ Successfully validated JSON result for: {img_name}")
                                        except ValidationError as e:
                                            print(f"  ⚠️ Validation failed for {img_name}: {e}")
                                            # Store with validation error but keep the data
                                            results[img_name] = {
                                                **json_data,
                                                "validation_warning": str(e)
                                            }
                                    except json.JSONDecodeError as e:
                                        print(f"  ⚠️ JSON decode failed for {img_name}: {e}")
                                        # Store as classification_text for manual review
                                        results[img_name] = {
                                            "classification_text": response_text,
                                            "requires_manual_review": True,
                                            "manual_review_reason": f"JSON decode failed: {str(e)}"
                                        }
                                except (KeyError, IndexError) as e:
                                    print(f"  Warning: Could not extract response for {img_name}. Error: {e}")
                                    results[img_name] = {"error": "extraction_failed", "raw_response": str(data.get("response", {}))}
                            elif "error" in data:
                                print(f"  API error for {img_name}: {data['error']}")
                                results[img_name] = {"error": "api_error", "details": data["error"]}
                            else:
                                print(f"  Warning: Unexpected response structure for {img_name}")
                                results[img_name] = {"error": "unexpected_structure", "raw_data": data}
                        except json.JSONDecodeError as e:
                            print(f"  Warning: Invalid JSON on line {line_num}: {e}")
                else:
                    print("Warning: Batch job completed but no output file destination was found.")
            except Exception as e:
                print(f"An unexpected error occurred during result processing: {e}")

        elif final_batch_job.state.name == 'JOB_STATE_FAILED':
            print(f"Batch job FAILED. State: {final_batch_job.state.name}")
            if hasattr(final_batch_job, 'error') and final_batch_job.error:
                print(f"Error details: {final_batch_job.error}")
        else:
            print(f"Batch job finished with an unexpected state: {final_batch_job.state.name}")
        # --- End of Robust Polling and Result Handling ---

    else:
        # Sequential processing
        logger.info(f"Starting sequential processing of {len(image_paths)} images")

        for i, img_path in enumerate(image_paths, 1):
            img_name = os.path.basename(img_path)
            logger.info(f"Processing image {i}/{len(image_paths)}: {img_name}")

            uploaded_img = client.files.upload(img_path)

            # Prepare contents for validation and retry
            if USE_EXAMPLES_AND_CACHING and cache:
                prompt_text = "Please classify the image into all mentioned categories"
            else:
                prompt_text = "Please classify the image into all mentioned categories."

            contents = [base_prompt, uploaded_img, prompt_text]

            # Use validation and retry mechanism
            success, result, error_message = validate_and_retry_response(contents, img_name)

            if success:
                results[img_name] = result
                logger.info(f"✓ Successfully processed {img_name}")
            else:
                results[img_name] = result
                logger.error(f"✗ Failed to process {img_name}: {error_message}")

                # If all retries failed, prompt user to re-add the photo
                if "max_retries_exceeded" in result.get("error", "") or "validation_failed" in result.get("error", ""):
                    logger.error(f"❌ MANUAL INTERVENTION REQUIRED for {img_name}")
                    logger.error(f"   Please check the image quality and try uploading {img_name} again.")
                    logger.error(f"   Error details: {error_message}")

                    # Store additional metadata for manual review
                    results[img_name]["requires_manual_review"] = True
                    results[img_name]["manual_review_reason"] = error_message

        # Generate processing summary
        successful_count = sum(1 for result in results.values() if not result.get("error"))
        error_count = sum(1 for result in results.values() if result.get("error"))
        manual_review_count = sum(1 for result in results.values() if result.get("requires_manual_review", False))

        logger.info("\n=== SEQUENTIAL PROCESSING SUMMARY ===")
        logger.info(f"Total images processed: {len(image_paths)}")
        logger.info(f"Successfully processed: {successful_count}")
        logger.info(f"Failed with errors: {error_count}")
        logger.info(f"Require manual review: {manual_review_count}")

        if manual_review_count > 0:
            logger.warning(f"\n⚠️  MANUAL REVIEW REQUIRED FOR {manual_review_count} IMAGES:")
            for img_name, result in results.items():
                if result.get("requires_manual_review", False):
                    logger.warning(f"   - {img_name}: {result.get('manual_review_reason', 'Unknown reason')}")
            logger.warning("   Please check these images and try uploading them again.")

        logger.info("====================================\n")

    # Write to JSON file
    with open(output_file, "w") as f:
        json.dump(results, f, indent=4)
    print(f"Annotations written to {output_file}")

    return results

def convert_to_labelme(results_file, output_dir, images_folder=None):
    """Converts the combined results JSON to individual LabelMe JSON files.
    
    Handles all possible cases found in Gemini API output including:
    - Null values for gender, ethnicity, age_group
    - All skin conditions found in the data
    - Proper mapping between Gemini and LabelMe formats
    - Robust error handling for malformed data
    - Adds imageData (base64) and image dimensions by reading actual image files
    
    Args:
        results_file: Path to the combined results JSON file
        output_dir: Directory to save LabelMe JSON files
        images_folder: Path to folder containing the original images (optional)
    """
    logger.info(f"Starting conversion of {results_file} to LabelMe format...")
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    with open(results_file, "r") as f:
        all_results = json.load(f)

    total_files = len(all_results)
    converted_count = 0
    error_count = 0

    for i, (image_name, data) in enumerate(all_results.items(), 1):
        logger.info(f"Processing file {i}/{total_files}: {image_name}")

        # Check if this is an error case that requires manual review
        if data.get("requires_manual_review", False):
            logger.warning(f"  Skipping {image_name}: Requires manual review - {data.get('manual_review_reason', 'Unknown reason')}")
            error_count += 1
            continue

        # Handle both old format (classification_text) and new validated format (direct JSON)
        gemini_data = None

        if "classification_text" in data:
            # Old format - extract from classification_text
            json_str = data["classification_text"].strip()
            if json_str.startswith("```json"):
                json_str = json_str[7:].strip()
                if json_str.endswith("```"):
                    json_str = json_str[:-3].strip()

            try:
                gemini_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.error(f"  Skipping {image_name}: Could not decode JSON from classification_text - {e}")
                error_count += 1
                continue

        elif all(key in data for key in ["quality_issues", "gender", "ethnicity", "age_group", "skin_conditions"]):
            # New validated format - data is already structured
            gemini_data = data

        else:
            logger.warning(f"  Skipping {image_name}: Neither 'classification_text' nor validated structure found.")
            error_count += 1
            continue

        # Initialize comprehensive LabelMe flags (extended to include all Gemini conditions)
        flags = {
            # Age groups
            "15-20": False, "20-25": False, "25-30": False, "30-35": False, "35-40": False,
            "40-45": False, "45-50": False, "50-55": False, "55-60": False, "60-65": False,
            "65-70": False, "70-75": False, "75-80": False,
            
            # Quality issues
            "blurry": False, "Unclear": False, "others": False, "side_face": False,
            
            # Gender
            "male": False, "female": False,
            
            # Ethnicity (keeping 'sothern' typo from original LabelMe example)
            "northern": False, "sothern": False, "eastern": False,
            
            # Original LabelMe skin conditions
            "excessive_oily": False, "wrinkles": False, "slight_wrinkles": False, "acne": False,
            "hyper_pigmentation": False, "uneven_pigmentation": False, "spots": False,
            "eczema": False, "psoriasis": False, "rosacea": False, "redness": False, "fineline": False,
            
            # Additional conditions found in Gemini data
            "under_eye_bag": False, "big_skin_pores": False, "small_skin_pores": False,
            "pimple": False, "scar_due_to_acne": False, "droopy_eyelid": False
        }

        # Set age group flag
        age_group = gemini_data.get("age_group")
        if age_group and age_group in flags:
            flags[age_group] = True
        
        # Set gender flag
        gender = gemini_data.get("gender")
        if gender and gender in flags:
            flags[gender] = True

        # Set quality issues flags
        quality_issues = gemini_data.get("quality_issues", [])
        if isinstance(quality_issues, list):
            for issue in quality_issues:
                if issue == "unclear":
                    flags["Unclear"] = True  # Handle capitalization
                elif issue in flags:
                    flags[issue] = True

        # Set ethnicity flags
        ethnicity = gemini_data.get("ethnicity")
        if ethnicity:
            if ethnicity == "ethnicity_northern":
                flags["northern"] = True
            elif ethnicity == "ethnicity_southern":
                flags["sothern"] = True  # Keep typo from LabelMe example
            elif ethnicity == "ethnicity_north_eastern":
                flags["eastern"] = True
            # ethnicity_others maps to no flags (all remain False)

        # Set skin condition flags
        skin_conditions = gemini_data.get("skin_conditions", [])
        if isinstance(skin_conditions, list):
            for condition in skin_conditions:
                if not isinstance(condition, dict):
                    continue
                    
                cond_name = condition.get("condition")
                if not cond_name:
                    continue
                
                # Handle condition mappings
                if cond_name in ["less_acne", "excessive_acne"]:
                    flags["acne"] = True
                elif cond_name in ["slight_wrinkles", "excessive_wrinkles"]:
                    flags["wrinkles"] = True
                    if cond_name == "slight_wrinkles":
                        flags["slight_wrinkles"] = True
                elif cond_name == "finelines":
                    flags["fineline"] = True
                elif cond_name == "excessive_oiliness":
                    flags["excessive_oily"] = True
                elif cond_name in flags:
                    # Direct mapping for conditions that match flag names
                    flags[cond_name] = True
                else:
                    logger.warning(f"  Unknown condition '{cond_name}' in {image_name}")

        # Get image data and dimensions
        image_data = None
        image_height = None
        image_width = None
        
        if images_folder:
            image_path = Path(images_folder) / image_name
            if image_path.exists():
                try:
                    # Read and encode image as base64
                    with open(image_path, "rb") as img_file:
                        image_data = base64.b64encode(img_file.read()).decode('utf-8')
                    
                    # Get image dimensions
                    with Image.open(image_path) as img:
                        image_width, image_height = img.size
                    
                    logger.info(f"  ✓ Added image data ({image_width}x{image_height})")
                except Exception as e:
                    logger.warning(f"  Could not read image {image_name}: {e}")
            else:
                logger.warning(f"  Image file not found: {image_path}")

        # Create LabelMe output
        labelme_output = {
            "version": "5.8.1",
            "flags": flags,
            "shapes": [],
            "imagePath": image_name,
            "imageHeight": image_height,
            "imageWidth": image_width
        }
        
        # Add imageData if available
        if image_data:
            labelme_output["imageData"] = image_data

        # Save the new JSON file
        labelme_filename = output_path / f"{Path(image_name).stem}.json"
        with open(labelme_filename, "w") as f:
            json.dump(labelme_output, f, indent=2)
        converted_count += 1
        logger.info(f"  ✓ Converted successfully")

    logger.info(f"\n=== LABELME CONVERSION SUMMARY ===")
    logger.info(f"Total files processed: {total_files}")
    logger.info(f"Successfully converted: {converted_count}")
    logger.info(f"Errors encountered: {error_count}")
    logger.info(f"LabelMe JSONs saved in: {output_dir}")
    logger.info("====================================\n")
    
    return converted_count, error_count


# Example usage options:

# Option 1: Process a folder of images in batches of 100 (RECOMMENDED for large datasets)
folder_path = "/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few"  # Replace with your folder path

all_results = process_folder_in_batches(
    folder_path=folder_path,
    batch_size=100,  # Process 100 images per batch
    output_dir="batch_results"  # Results will be saved here
)

# After processing, convert the results to LabelMe format
if all_results:
    combined_results_file = Path("batch_results") / "all_results_combined.json"
    if combined_results_file.exists():
        convert_to_labelme(
            results_file=str(combined_results_file),
            output_dir="labelme_outputs",
            images_folder=folder_path  # Use the same folder path as the source images
        )
    else:
        logger.error("Could not find combined results file to convert.")
