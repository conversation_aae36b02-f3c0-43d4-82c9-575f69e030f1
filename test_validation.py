#!/usr/bin/env python3
"""
Test script to verify Pydantic validation for GenAI responses
"""

import json
from pydantic import BaseModel, ValidationError, Field
from typing import List, Optional, Literal

# Import the models from the main script
from gemini_auto_annotation import SkinCondition, FaceAnalysisResponse

def test_validation():
    """Test the validation with sample data"""
    
    # Valid response
    valid_response = {
        "quality_issues": [],
        "gender": "female",
        "ethnicity": "ethnicity_northern",
        "age_group": "25-30",
        "skin_conditions": [
            {
                "condition": "excessive_acne",
                "location": "forehead and left cheek",
                "description": "Multiple red inflamed pustules and papules clustered on forehead with 3-4 visible on left cheek.",
                "confidence": 85
            }
        ]
    }
    
    # Invalid response (missing required fields)
    invalid_response = {
        "quality_issues": [],
        "gender": "female",
        "skin_conditions": [
            {
                "condition": "excessive_acne",
                "location": "forehead",
                # missing description and confidence
            }
        ]
    }
    
    # Invalid response (wrong data types)
    invalid_types_response = {
        "quality_issues": "not_a_list",  # Should be a list
        "gender": "invalid_gender",      # Should be male or female
        "ethnicity": "ethnicity_northern",
        "age_group": "25-30",
        "skin_conditions": [
            {
                "condition": "excessive_acne",
                "location": "forehead",
                "description": "Some description",
                "confidence": "not_a_number"  # Should be an integer
            }
        ]
    }
    
    print("Testing Pydantic Validation for Face Analysis Response")
    print("=" * 60)
    
    print("\n1. Testing valid response...")
    try:
        validated = FaceAnalysisResponse(**valid_response)
        print("✅ Valid response passed validation")
        print(f"Validated data keys: {list(validated.model_dump().keys())}")
    except ValidationError as e:
        print(f"❌ Valid response failed validation: {e}")
    
    print("\n2. Testing invalid response (missing fields)...")
    try:
        validated = FaceAnalysisResponse(**invalid_response)
        print("❌ Invalid response incorrectly passed validation")
    except ValidationError as e:
        print(f"✅ Invalid response correctly failed validation")
        print(f"Validation errors: {len(e.errors())} errors found")
        for error in e.errors():
            print(f"  - {error['loc']}: {error['msg']}")
    
    print("\n3. Testing invalid response (wrong data types)...")
    try:
        validated = FaceAnalysisResponse(**invalid_types_response)
        print("❌ Invalid types response incorrectly passed validation")
    except ValidationError as e:
        print(f"✅ Invalid types response correctly failed validation")
        print(f"Validation errors: {len(e.errors())} errors found")
        for error in e.errors():
            print(f"  - {error['loc']}: {error['msg']}")
    
    print("\n4. Testing SkinCondition model separately...")
    valid_condition = {
        "condition": "hyper_pigmentation",
        "location": "right cheek",
        "description": "Dark patches on the right cheek area",
        "confidence": 78
    }
    
    try:
        skin_condition = SkinCondition(**valid_condition)
        print("✅ SkinCondition validation passed")
        print(f"Condition: {skin_condition.condition}, Confidence: {skin_condition.confidence}")
    except ValidationError as e:
        print(f"❌ SkinCondition validation failed: {e}")
    
    print("\n" + "=" * 60)
    print("Validation testing completed!")

if __name__ == "__main__":
    test_validation()
