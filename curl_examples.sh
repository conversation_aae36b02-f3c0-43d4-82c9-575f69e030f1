#!/bin/bash

# Face Analysis API - Curl Examples
# Make sure the API server is running on localhost:8000

echo "Face Analysis API - Curl Examples"
echo "=================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. Health Check
echo -e "\n${BLUE}1. Health Check:${NC}"
curl -X GET "http://localhost:8000/health" \
  -H "accept: application/json" | python -m json.tool 2>/dev/null || curl -X GET "http://localhost:8000/health"

# 2. Root endpoint
echo -e "\n\n${BLUE}2. Root endpoint:${NC}"
curl -X GET "http://localhost:8000/" \
  -H "accept: application/json" | python -m json.tool 2>/dev/null || curl -X GET "http://localhost:8000/"

# 3. Single Image Analysis
echo -e "\n\n${BLUE}3. Single Image Analysis:${NC}"
echo "Replace 'path/to/your/image.jpg' with actual image path"

# Example with actual file paths (update these paths)
IMAGE1="/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1ao0v53_p3ybp8qoawhc1.png"
IMAGE2="/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1asmim9_dp4mfq1541jc1_face1.png"

if [ -f "$IMAGE1" ]; then
    echo -e "\n${GREEN}Testing with actual image: $IMAGE1${NC}"
    curl -X POST "http://localhost:8000/analyze" \
      -H "accept: application/json" \
      -H "Content-Type: multipart/form-data" \
      -F "file=@$IMAGE1" | python -m json.tool 2>/dev/null || curl -X POST "http://localhost:8000/analyze" -F "file=@$IMAGE1"
else
    echo "Sample command (replace with actual image path):"
    echo "curl -X POST \"http://localhost:8000/analyze\" \\"
    echo "  -H \"accept: application/json\" \\"
    echo "  -H \"Content-Type: multipart/form-data\" \\"
    echo "  -F \"file=@path/to/your/image.jpg\""
fi

# 4. Multiple Images Analysis
echo -e "\n\n${BLUE}4. Multiple Images Analysis:${NC}"

if [ -f "$IMAGE1" ] && [ -f "$IMAGE2" ]; then
    echo -e "\n${GREEN}Testing with actual images${NC}"
    curl -X POST "http://localhost:8000/analyze-multiple" \
      -H "accept: application/json" \
      -H "Content-Type: multipart/form-data" \
      -F "files=@$IMAGE1" \
      -F "files=@$IMAGE2" | python -m json.tool 2>/dev/null || curl -X POST "http://localhost:8000/analyze-multiple" -F "files=@$IMAGE1" -F "files=@$IMAGE2"
else
    echo "Sample command (replace with actual image paths):"
    echo "curl -X POST \"http://localhost:8000/analyze-multiple\" \\"
    echo "  -H \"accept: application/json\" \\"
    echo "  -H \"Content-Type: multipart/form-data\" \\"
    echo "  -F \"files=@image1.jpg\" \\"
    echo "  -F \"files=@image2.jpg\" \\"
    echo "  -F \"files=@image3.jpg\""
fi

# 5. Test file size limit (create a dummy large file)
echo -e "\n\n${BLUE}5. Test File Size Limit:${NC}"
echo "Creating a dummy large file to test size limit..."

# Create a 25MB dummy file (larger than default 20MB limit)
if ! command -v dd &> /dev/null; then
    echo "dd command not available, skipping file size test"
else
    dd if=/dev/zero of=large_test_file.dat bs=1M count=25 2>/dev/null
    if [ -f "large_test_file.dat" ]; then
        echo "Testing with 25MB file (should fail with 413 error):"
        curl -X POST "http://localhost:8000/analyze" \
          -H "accept: application/json" \
          -H "Content-Type: multipart/form-data" \
          -F "file=@large_test_file.dat" 2>/dev/null || echo "Request failed as expected"
        
        # Clean up
        rm -f large_test_file.dat
        echo "Cleaned up test file"
    fi
fi

# 6. Test invalid file format
echo -e "\n\n${BLUE}6. Test Invalid File Format:${NC}"
echo "Testing with text file (should fail with 400 error):"

# Create a dummy text file
echo "This is not an image" > test_file.txt
curl -X POST "http://localhost:8000/analyze" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_file.txt" 2>/dev/null || echo "Request failed as expected"

# Clean up
rm -f test_file.txt

echo -e "\n\n${GREEN}Testing completed!${NC}"
echo -e "\n📋 Additional testing options:"
echo "• Interactive docs: http://localhost:8000/docs"
echo "• ReDoc: http://localhost:8000/redoc"
echo "• Run automated tests: python test_api.py"
echo ""
echo "💡 Tips:"
echo "• The API supports: jpg, jpeg, png, bmp, tiff, webp formats"
echo "• Maximum file size: 20MB (configurable via MAX_FILE_SIZE_MB)"
echo "• Maximum 5 files per batch request"
echo "• Use Postman for easier testing with file uploads"
