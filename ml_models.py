import os
import numpy as np
import mediapipe as mp
from mediapipe.tasks import python
from mediapipe.tasks.python import vision
import onnxruntime as ort
import cv2
from PIL import Image
from scipy.special import softmax

# Create output directories
WRITE=False
CROP_DIR = "crops"
PLOT_DIR = "plots"
JSON_DIR = "json_outputs"
ORIGINAL_IMAGE_DIR = "original_images"
os.makedirs(CROP_DIR, exist_ok=True)
os.makedirs(PLOT_DIR, exist_ok=True)
os.makedirs(JSON_DIR, exist_ok=True)
os.makedirs(ORIGINAL_IMAGE_DIR, exist_ok=True)

# Define colors for different facial features
FEATURE_COLORS = {
    'left_eye': (0, 255, 0),      # Green
    'right_eye': (0, 255, 0),     # Green
    'forehead': (255, 0, 0),      # Blue
    'nose': (0, 0, 255),          # Red
    'lips': (255, 0, 255),        # Magenta
    'mouth': (255, 255, 0),       # Cyan
    'left_cheek': (128, 0, 128),  # Purple
    'right_cheek': (128, 0, 128), # Purple
    'left_eyebrow': (0, 128, 255), # Orange
    'right_eyebrow': (0, 128, 255) # Orange
}

# Load ImageNet class labels
with open("imagenet_classes.txt") as f:
    labels = [line.strip().split(": ")[1] for line in f.readlines()]

# Initialize ResNet18 ONNX model
resnet_session = ort.InferenceSession("resnet18.onnx")
resnet_input_name = resnet_session.get_inputs()[0].name
resnet_output_name = resnet_session.get_outputs()[0].name
MEAN = np.array([0.485, 0.456, 0.406], dtype=np.float32)
STD = np.array([0.229, 0.224, 0.225], dtype=np.float32)

# Initialize FaceMeshV2
BaseOptions = python.BaseOptions
VisionRunningMode = vision.RunningMode
FaceLandmarkerOptions = vision.FaceLandmarkerOptions
FaceLandmarker = vision.FaceLandmarker
base_options = BaseOptions(
    model_asset_path="face_landmarker_v2_with_blendshapes.task",
    delegate=BaseOptions.Delegate.CPU
)
options = FaceLandmarkerOptions(
    base_options=base_options,
    output_face_blendshapes=True,
    output_facial_transformation_matrixes=True,
    num_faces=10,
    running_mode=VisionRunningMode.IMAGE
)
landmarker = FaceLandmarker.create_from_options(options)

# Define landmark indices for facial features (based on MediaPipe FaceMesh V2 topology)
FACIAL_FEATURES = {
    'left_eye': [34, 21, 68, 9, 6, 197, 100, 116],
    'right_eye': [264, 251, 298, 9, 6, 197, 329, 345],
    'forehead': [21, 109, 332, 251, 9],
    'nose': [69, 119, 92, 322, 349, 299],
    'mouth': [58, 215, 37, 435,152,150],
    'left_cheek': [127, 23, 6, 14, 58, 137],
    'right_cheek': [356, 253, 6, 14, 367, 366],
}

def convert_to_serializable(data):
    """Convert NumPy types to Python native types for orjson serialization."""
    if isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, dict):
        return {key: convert_to_serializable(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_to_serializable(item) for item in data]
    elif isinstance(data, tuple):
        return tuple(convert_to_serializable(item) for item in data)
    return data

def process_image_for_facemesh(image):
    """Convert PIL Image to MediaPipe Image format."""
    image_np = np.array(image)
    image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
    mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=image_np)
    return mp_image, image_np

def calculate_bounding_box(landmarks, indices, image_width, image_height, feature_name):
    """Calculate bounding box and polygon for a set of landmark indices."""
    x_coords = [landmarks[i].x * image_width for i in indices if i < len(landmarks)]
    y_coords = [landmarks[i].y * image_height for i in indices if i < len(landmarks)]
    if not x_coords or not y_coords:
        return None, None
    
    # Calculate bounding box
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    if feature_name == 'forehead':
        y_min = max(0, (y_min - (2 * (y_max - y_min))))
    bbox = {
        'x_min': max(0, x_min),
        'y_min': max(0, y_min),
        'x_max': min(image_width, x_max),
        'y_max': min(image_height, y_max),
        'width': min(image_width, x_max) - max(0, x_min),
        'height': min(image_height, y_max) - max(0, y_min)
    }
    x_min = bbox["x_min"]
    y_min = bbox["y_min"]
    x_max = bbox["x_max"]
    y_max = bbox["y_max"]
    points = [[x_min, y_min], [x_max, y_max]]
    
    return bbox, points

def crop_and_arrange_features(image_np, faces, image_width, image_height, unique_id):
    """Crop facial features, save them, and arrange them in a 2x5 grid."""
    feature_crops = []
    target_size = (224, 224)  # ResNet18 input size
    
    # Define the order of features for consistent arrangement
    feature_order = ['left_eye', 'nose', 'right_eye', 'forehead', 'mouth', 'left_cheek', 'right_cheek']
    
    for face_idx, face in enumerate(faces):
        for feature_name in feature_order:
            if feature_name in face['bounding_boxes']:
                bbox = face['bounding_boxes'][feature_name]
                if bbox:
                    x_min = int(bbox['x_min'])
                    y_min = int(bbox['y_min'])
                    x_max = int(bbox['x_max'])
                    y_max = int(bbox['y_max'])
                    # Ensure coordinates are within image bounds
                    x_min = max(0, x_min)
                    y_min = max(0, y_min)
                    x_max = min(image_width, x_max)
                    y_max = min(image_height, y_max)
                    # Crop the feature region
                    if x_max > x_min and y_max > y_min:
                        crop = image_np[y_min:y_max, x_min:x_max]
                        if crop.size > 0:
                            # Save original crop
                            if WRITE:
                                crop_filename = f"{unique_id}_face{face_idx}_{feature_name}_crop.jpg"
                                crop_path = os.path.join(CROP_DIR, crop_filename)
                                cv2.imwrite(crop_path, crop)
                            
                            # Convert to PIL and resize for grid arrangement
                            crop_pil = Image.fromarray(cv2.cvtColor(crop, cv2.COLOR_BGR2RGB))
                            crop_resized = crop_pil.resize(target_size, Image.Resampling.LANCZOS)
                            feature_crops.append(np.array(crop_resized))
    
    if not feature_crops:
        return None
    
    # Arrange crops in 2x5 grid
    rows, cols = 3, 3
    grid_width = target_size[0] * cols
    grid_height = target_size[1] * rows
    grid_image = np.zeros((grid_height, grid_width, 3), dtype=np.uint8)
    
    for i, crop in enumerate(feature_crops[:10]):  # Limit to 10 crops
        row = i // cols
        col = i % cols
        y_start = row * target_size[1]
        y_end = y_start + target_size[1]
        x_start = col * target_size[0]
        x_end = x_start + target_size[0]
        grid_image[y_start:y_end, x_start:x_end, :] = crop
    
    # Save grid image
    if WRITE:
        grid_filename = f"{unique_id}_grid_features.jpg"
        grid_path = os.path.join(CROP_DIR, grid_filename)
        cv2.imwrite(grid_path, cv2.cvtColor(grid_image, cv2.COLOR_RGB2BGR))
    
    # Convert to PIL Image
    grid_pil = Image.fromarray(grid_image)
    return grid_pil

def draw_bounding_boxes(image_np, faces, image_width, image_height):
    """Draw bounding boxes on the image for each facial feature."""
    annotated_image = image_np.copy()
    for face in faces:
        for feature_name, bbox in face['bounding_boxes'].items():
            if bbox:
                top_left = (int(bbox['x_min']), int(bbox['y_min']))
                bottom_right = (int(bbox['x_max']), int(bbox['y_max']))
                color = FEATURE_COLORS.get(feature_name, (255, 255, 255))  # Default to white
                # Draw rectangle
                cv2.rectangle(annotated_image, top_left, bottom_right, color, 2)
                # Add label
                cv2.putText(
                    annotated_image,
                    feature_name,
                    (top_left[0], top_left[1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    color,
                    2
                )
    return annotated_image

def preprocess_image_for_resnet(image):
    """Preprocess image for ResNet18: resize, normalize, and convert to tensor."""
    # Resize grid image to 224x224 for ResNet18
    image = image.resize((224, 224), Image.Resampling.LANCZOS)
    # Convert to numpy array and normalize to [0, 1]
    image_np = np.array(image, dtype=np.float32) / 255.0
    # Apply ImageNet normalization
    image_np = (image_np - MEAN) / STD
    # Convert to CHW format (channels, height, width)
    image_np = image_np.transpose(2, 0, 1)  # HWC to CHW
    # Create batch of size 1 for the grid image
    processed_images = np.expand_dims(image_np, axis=0)
    return processed_images

def run_resnet_inference(image,disable=True):
    if disable:
        return []
    """Run ResNet18 inference on the provided image."""
    input_data = preprocess_image_for_resnet(image)
    outputs = resnet_session.run([resnet_output_name], {resnet_input_name: input_data})[0]
    probabilities = softmax(outputs[0], axis=0)
    top5_indices = np.argsort(probabilities)[-5:][::-1]
    top5_probs = probabilities[top5_indices].tolist()
    return [
        {'class_id': int(idx), 'class_name': labels[idx], 'probability': float(prob)}
        for idx, prob in zip(top5_indices, top5_probs)
    ]

async def process_facemesh(mp_image, image_np, image_width, image_height, unique_id, timestamp):
    """Process image with FaceMeshV2 and return faces and grid image."""
    # mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=image_np)
    detection_result = landmarker.detect(mp_image)
    
    faces = []
    for face_idx, face_landmarks in enumerate(detection_result.face_landmarks):
        bounding_boxes = {}
        points = {}
        for feature_name, indices in FACIAL_FEATURES.items():
            bbox, point = calculate_bounding_box(face_landmarks, indices, image_width, image_height, feature_name)
            if bbox:
                bounding_boxes[feature_name] = bbox
                points[feature_name] = point
        faces.append({'bounding_boxes': bounding_boxes, 'points': points})
    
    if WRITE:
        bbox_image = draw_bounding_boxes(image_np, faces, image_width, image_height)
        bbox_filename = f"{unique_id}_{timestamp}_bbox_plot.jpg"
        bbox_path = os.path.join(PLOT_DIR, bbox_filename)
        cv2.imwrite(bbox_path, bbox_image)
    
    grid_image = crop_and_arrange_features(image_np, faces, image_width, image_height, f"{unique_id}_{timestamp}")
    return grid_image, faces