import asyncio
from concurrent.futures import Thr<PERSON>PoolExecutor
import os
import json
import time
import logging
import base64
import io
import tempfile
from pathlib import Path
from dotenv import load_dotenv
from google import genai
from google.genai import types
from pydantic import BaseModel, ValidationError, Field
from typing import List, Optional, Literal

from fastapi import FastAPI, File, UploadFile, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from PIL import Image
from ml_models import process_facemesh, process_image_for_facemesh, run_resnet_inference
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Face Analysis API",
    description="AI-powered facial analysis and skin condition detection API using Google Gemini",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
model_name = "gemini-2.5-pro"
client = genai.Client()
USE_EXAMPLES_AND_CACHING = False
MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "20"))
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# Base prompt for face analysis
base_prompt = """
You are an expert dermatologist and facial analysis specialist. Analyze the provided facial image and classify it into the following categories. Return your response as a JSON object with the exact structure shown below.

IMPORTANT: Your response must be ONLY a valid JSON object, no additional text or markdown formatting.

Categories to analyze:

1. **Quality Issues** (if any):
   - "blurry": Image is blurry or out of focus
   - "unclear": Image quality is poor or unclear
   - "others": Not a human face (object, animal, etc.)
   - "side_face": Side profile instead of frontal view

2. **Gender Classification**:
   - "male" or "female"

3. **Ethnicity Classification**:
   - "ethnicity_northern": Northern Indian features
   - "ethnicity_southern": Southern Indian features  
   - "ethnicity_north_eastern": North-Eastern Indian features
   - "ethnicity_others": Other ethnicities

4. **Age Group**:
   - Choose from: "15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80"

5. **Skin Conditions** (list all that apply):
   Available conditions: excessive_oiliness, excessive_wrinkles, slight_wrinkles, less_acne, excessive_acne, scar_due_to_acne, pimple, hyper_pigmentation, uneven_pigmentation, spots, small_skin_pores, big_skin_pores, under_eye_bag, droopy_eyelid, redness, finelines, eczema, psoriasis, rosacea

For each skin condition found, provide:
- condition: exact name from the list above
- location: specific area on face where condition is observed
- description: detailed description of what you observe
- confidence: confidence score from 0-100

Required JSON Response Format:
{
  "quality_issues": [],
  "gender": "male/female or null",
  "ethnicity": "ethnicity_type or null", 
  "age_group": "age_range or null",
  "skin_conditions": [
    {
      "condition": "condition_name",
      "location": "specific_location",
      "description": "detailed_description", 
      "confidence": 85
    }
  ]
}
"""

# Pydantic models for response validation
class SkinCondition(BaseModel):
    condition: str = Field(..., description="The skin condition name")
    location: str = Field(..., description="Location of the condition on the face")
    description: str = Field(..., description="Detailed description of the condition")
    confidence: int = Field(..., ge=0, le=100, description="Confidence score between 0-100")

    
class FaceAnalysisResponse(BaseModel):
    quality_issues: List[Literal["blurry", "unclear", "others", "side_face"]] = Field(default_factory=list)
    gender: Optional[Literal["male", "female"]] = Field(None, description="Gender classification")
    ethnicity: Optional[Literal["ethnicity_northern", "ethnicity_southern", "ethnicity_north_eastern", "ethnicity_others"]] = Field(None, description="Ethnicity classification")
    age_group: Optional[Literal["15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80"]] = Field(None, description="Age group classification")
    skin_conditions: List[SkinCondition] = Field(default_factory=list, description="List of detected skin conditions")

class FinalAnalysisResponse(BaseModel):
    models_analysis: Optional[dict] = None
    final_analysis: Optional[FaceAnalysisResponse] = None

# API Response models
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[FinalAnalysisResponse] = None
    error_details: Optional[dict] = None

class HealthResponse(BaseModel):
    status: str
    message: str
    version: str
    max_file_size_mb: int

def _is_valid_image_format(image_bytes: bytes) -> bool:
    """
    Fast image format validation using magic bytes.
    No PIL overhead - just checks file signatures.
    """
    if len(image_bytes) < 8:
        return False

    # Check magic bytes for common image formats
    magic_bytes = image_bytes[:8]

    # JPEG: FF D8 FF
    if magic_bytes.startswith(b'\xff\xd8\xff'):
        return True

    # PNG: 89 50 4E 47 0D 0A 1A 0A
    if magic_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
        return True

    # BMP: 42 4D
    if magic_bytes.startswith(b'BM'):
        return True

    # TIFF: 49 49 2A 00 or 4D 4D 00 2A
    if magic_bytes.startswith(b'II*\x00') or magic_bytes.startswith(b'MM\x00*'):
        return True

    # WebP: 52 49 46 46 ... 57 45 42 50
    if magic_bytes.startswith(b'RIFF') and len(image_bytes) >= 12:
        if image_bytes[8:12] == b'WEBP':
            return True

    return False

def validate_and_retry_response(contents, img_name, max_retries=MAX_RETRIES):
    """
    Validate GenAI response with Pydantic and retry on validation failure.
    """
    original_instruction = contents[-1] if isinstance(contents[-1], str) else "Please classify the image into all mentioned categories."
    
    for attempt in range(max_retries + 1):
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries + 1} for {img_name}")
            
            # Generate content from model
            generation_config = {}
            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=types.GenerateContentConfig(**generation_config)
            )
            
            response_text = response.candidates[0].content.parts[0].text
            logger.info(f"Raw response for {img_name} (attempt {attempt + 1}): {response_text[:200]}...")
            
            # Clean the response text
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:].strip()
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3].strip()
            
            # Parse JSON
            try:
                json_data = json.loads(cleaned_text)
            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error for {img_name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    contents[-1] = original_instruction + f"\n\nIMPORTANT: Your previous response had JSON parsing errors: {str(e)}. Please ensure your response is ONLY a valid JSON object with no additional text or markdown formatting."
                    continue
                else:
                    return False, {"error": "json_decode_failed", "raw_response": response_text}, f"Failed to decode JSON after {max_retries + 1} attempts"
            
            # Validate with Pydantic
            try:
                validated_response = FaceAnalysisResponse(**json_data)
                logger.info(f"✓ Validation successful for {img_name} on attempt {attempt + 1}")
                return True, validated_response.model_dump(), None
                
            except ValidationError as e:
                logger.warning(f"Pydantic validation error for {img_name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    validation_errors = str(e)
                    contents[-1] = original_instruction + f"\n\nIMPORTANT: Your previous response had validation errors: {validation_errors}. Please ensure your JSON response strictly follows the required format with all required fields and correct data types."
                    continue
                else:
                    return False, {"error": "validation_failed", "validation_errors": str(e), "raw_response": response_text}, f"Validation failed after {max_retries + 1} attempts"
                    
        except Exception as e:
            logger.error(f"Unexpected error for {img_name} (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                contents[-1] = original_instruction + f"\n\nIMPORTANT: Previous attempt failed with error: {str(e)}. Please provide a valid JSON response."
                continue
            else:
                return False, {"error": "generation_failed", "details": str(e)}, f"Generation failed after {max_retries + 1} attempts"
    
    return False, {"error": "max_retries_exceeded"}, f"Exceeded maximum retries ({max_retries + 1})"

def analyze_image_bytes(image_bytes: bytes, image_name: str = "uploaded_image") -> tuple[bool, dict, str]:
    """
    Analyze image from raw bytes using minimal temporary file operations.

    Optimizations:
    - Minimal temporary file usage (created and deleted immediately)
    - No PIL operations (uses magic bytes validation)
    - Fast upload to Google GenAI using file path
    - Automatic cleanup in finally block
    - Production-ready with reliable upload method

    Note: Uses temporary file as Google GenAI client requires file path.
    File exists only during upload operation and is immediately cleaned up.
    """
    try:
        start_time = time.time()

        # Basic format validation using magic bytes (no PIL overhead)
        if not _is_valid_image_format(image_bytes):
            return False, {"error": "invalid_image", "details": "Unsupported image format"}, "Invalid image format detected"

        logger.info(f"Processing image: {image_name}, Size: {len(image_bytes)} bytes")
        
        # Upload image bytes directly to GenAI
        logger.info(f"Uploading image bytes: {image_name}")
        upload_start = time.time()

        # Retry upload with exponential backoff
        max_upload_retries = 3
        uploaded_img = None

        # Upload using temporary file (reliable method for Google GenAI client)
        temp_file_path = None
        try:
            # Determine file extension for temporary file
            file_extension = ".jpg"  # default
            if image_name and '.' in image_name:
                file_extension = Path(image_name).suffix.lower()

            # MIME type mapping
            mime_type_map = {
                '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
                '.png': 'image/png', '.bmp': 'image/bmp',
                '.tiff': 'image/tiff', '.webp': 'image/webp'
            }
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_extension)
            temp_file.write(image_bytes)
            temp_file.close()
            temp_file_path=temp_file.name
            # Create temporary file
            # with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            #     temp_file.write(image_bytes)
            #     temp_file_path = temp_file.name

            print(f"Created temporary file: {temp_file_path}")

            # Upload using file path (as expected by Google GenAI client)
            uploaded_img = None
            for upload_attempt in range(max_upload_retries):
                try:
                    # Try upload with file path as positional argument (like original code)
                    uploaded_img = client.files.upload(file=temp_file_path)
                    upload_time = time.time() - upload_start
                    logger.info(f"Image upload took {upload_time:.2f} seconds (attempt {upload_attempt + 1})")
                    break

                except Exception as upload_error:
                    logger.warning(f"Upload attempt {upload_attempt + 1} failed: {upload_error}")
                    if upload_attempt < max_upload_retries - 1:
                        wait_time = 2 ** upload_attempt
                        logger.info(f"Retrying upload in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        return False, {"error": "upload_failed", "details": str(upload_error)}, f"Failed to upload image after {max_upload_retries} attempts: {str(upload_error)}"

            # Check if upload was successful
            if uploaded_img is None:
                return False, {"error": "upload_failed", "details": "Upload failed - no uploaded image"}, "Upload failed - no uploaded image"

        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    logger.debug(f"Cleaned up temporary file: {temp_file_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file: {e}")
        # Prepare contents for analysis
        prompt_text = "Please classify the image into all mentioned categories."
        contents = [base_prompt, uploaded_img, prompt_text]
        
        # Use validation and retry mechanism
        success, result, error_message = validate_and_retry_response(contents, image_name)
        
        # Clean up uploaded file
        try:
            # Note: The Google GenAI client might not have a delete method for the new API
            # This is a placeholder for cleanup if available
            pass
        except Exception as e:
            logger.warning(f"Failed to clean up uploaded file: {e}")
        
        # Log total processing time
        total_time = time.time() - start_time
        logger.info(f"Total analysis time for {image_name}: {total_time:.2f} seconds")
        
        return success, result, error_message
        
    except Exception as e:
        logger.error(f"Unexpected error analyzing {image_name}: {e}")
        return False, {"error": "analysis_failed", "details": str(e)}, f"Analysis failed: {str(e)}"

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the API on startup"""
    logger.info("Face Analysis API starting up...")
    logger.info(f"Using model: {model_name}")
    logger.info(f"Max retries configured: {MAX_RETRIES}")
    logger.info(f"Max file size: {MAX_FILE_SIZE_MB} MB")
    logger.info(f"Examples and caching enabled: {USE_EXAMPLES_AND_CACHING}")
    logger.info("API ready to process requests!")

# API Endpoints

@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint - API health check"""
    return HealthResponse(
        status="healthy",
        message="Face Analysis API is running",
        version="1.0.0",
        max_file_size_mb=MAX_FILE_SIZE_MB
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Test if API key is configured
        if not os.getenv("GOOGLE_API_KEY"):
            raise Exception("GOOGLE_API_KEY not configured")

        return HealthResponse(
            status="healthy",
            message="API is healthy and ready to process images",
            version="1.0.0",
            max_file_size_mb=MAX_FILE_SIZE_MB
        )
    except Exception as e:
        return HealthResponse(
            status="unhealthy",
            message=f"API health check failed: {str(e)}",
            version="1.0.0",
            max_file_size_mb=MAX_FILE_SIZE_MB
        )

@app.post("/analyze", response_model=APIResponse)
async def analyze_face(file: UploadFile = File(...)):
    """
    Analyze a single uploaded image for face analysis and skin conditions.

    Args:
        file: Uploaded image file (jpg, jpeg, png, bmp, tiff, webp)

    Returns:
        APIResponse with face analysis results
    """
    # Validate file type
    allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    file_extension = Path(file.filename).suffix.lower() if file.filename else '.jpg'
    timestamp = int(time.time())

    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported file type: {file_extension}. Allowed types: {', '.join(allowed_extensions)}"
        )

    try:
        # Read file content into memory
        content = await file.read()

        # Check file size
        file_size_mb = len(content) / (1024 * 1024)
        if len(content) > MAX_FILE_SIZE_BYTES:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size ({MAX_FILE_SIZE_MB} MB)"
            )

        logger.info(f"Processing file: {file.filename}, Size: {file_size_mb:.2f} MB")

        # Fast image format validation using magic bytes
        if not _is_valid_image_format(content):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image format detected"
            )
        
        base64_string = base64.b64encode(content).decode('utf-8')
            
        # Load image for processing
        image = Image.open(io.BytesIO(content)).convert('RGB')
        image_width, image_height = image.size

        # Prepare image for FaceMeshV2
        image_mp, image_np = process_image_for_facemesh(image)

        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as pool:
            full_image_task = loop.run_in_executor(pool, run_resnet_inference, image)
            facemesh_task = process_facemesh(image_mp, image_np, image_width, image_height, file.filename, timestamp)
            full_image_result, (grid_image, faces) = await asyncio.gather(full_image_task, facemesh_task)
        
        if grid_image is None:
            return JSONResponse(content={'error': 'No facial features detected or cropped'},status_code=400) 
        
        # Run ResNet18 on the feature grid
        feature_grid_result = run_resnet_inference(grid_image)
        response = {
            'image_width': int(image_width),
            'image_height': int(image_height),
            'full_image_resnet_predictions': full_image_result,
            'facial_feature_grid_predictions': feature_grid_result,
            'facial_feature_detection': faces
        }
        # GenAI based Analysis on the image using bytes directly
        logger.info(f"Starting analysis for uploaded file: {file.filename}")
        success, result, error_message = analyze_image_bytes(content, file.filename or "uploaded_image")
        final_result={"models_analysis": response, "final_analysis": result}
    
        if success:
            # print(f"response", response)
            final_response=APIResponse(
                success=True,
                message="Image analyzed successfully",
                data=FinalAnalysisResponse(**final_result)
            )
            return final_response
        else:
            # Check if manual review is required
            if result.get("requires_manual_review", False):
                return APIResponse(
                    success=False,
                    message="Analysis failed after multiple retries. Please check image quality and try again.",
                    error_details={
                        "error_type": "validation_failed_max_retries",
                        "reason": result.get("manual_review_reason", error_message),
                        "suggestion": "Please ensure the image is clear, well-lit, and shows a frontal view of a human face."
                    }
                )
            else:
                return APIResponse(
                    success=False,
                    message="Image analysis failed",
                    error_details={
                        "error_type": result.get("error", "unknown_error"),
                        "details": error_message
                    }
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in analyze_face endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@app.post("/analyze-multiple", response_model=dict)
async def analyze_multiple_faces(files: List[UploadFile] = File(...)):
    """
    Analyze multiple uploaded images for face analysis and skin conditions.

    Args:
        files: List of uploaded image files (max 5 files)

    Returns:
        Dictionary with results for each image
    """
    if len(files) > 5:  # Limit batch size for API
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum 5 files allowed per batch request"
        )

    results = {}
    allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}

    for file in files:
        file_extension = Path(file.filename).suffix.lower() if file.filename else '.jpg'

        if file_extension not in allowed_extensions:
            results[file.filename or "unknown_file"] = {
                "success": False,
                "error": f"Unsupported file type: {file_extension}"
            }
            continue

        try:
            # Read file content
            content = await file.read()

            # Check file size
            file_size_mb = len(content) / (1024 * 1024)
            if len(content) > MAX_FILE_SIZE_BYTES:
                results[file.filename or "unknown_file"] = {
                    "success": False,
                    "error": f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size ({MAX_FILE_SIZE_MB} MB)"
                }
                continue

            # Fast image format validation using magic bytes
            if not _is_valid_image_format(content):
                results[file.filename or "unknown_file"] = {
                    "success": False,
                    "error": "Invalid image format detected"
                }
                continue

            # Analyze the image
            success, result, error_message = analyze_image_bytes(content, file.filename or "uploaded_image")

            if success:
                results[file.filename or "unknown_file"] = {
                    "success": True,
                    "data": result
                }
            else:
                results[file.filename or "unknown_file"] = {
                    "success": False,
                    "error": error_message,
                    "error_details": result
                }

        except Exception as e:
            results[file.filename or "unknown_file"] = {
                "success": False,
                "error": f"Processing failed: {str(e)}"
            }

    return {
        "message": f"Processed {len(files)} images",
        "results": results
    }

# Main function to run the server
if __name__ == "__main__":
    # Check if required environment variables are set
    if not os.getenv("GOOGLE_API_KEY"):
        logger.error("GOOGLE_API_KEY environment variable is not set!")
        exit(1)

    # Run the server
    uvicorn.run(
        "gemini_api:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8076)),
        reload=True,
        log_level="info"
    )
