# Pydantic Validation and Retry Mechanism

This document describes the changes made to add Pydantic validation with retry mechanism to the `gemini_auto_annotation.py` script.

## Overview

The script now includes:
- **Pydantic Models**: Strict validation for GenAI responses
- **Retry Mechanism**: Up to 3 configurable retries on validation failures
- **Smart Prompt Management**: Removes old retry instructions and adds new ones
- **Manual Review Flagging**: Images requiring manual intervention are clearly marked
- **Backward Compatibility**: Handles both old and new response formats

## Key Features

### 1. Pydantic Models

```python
class SkinCondition(BaseModel):
    condition: str = Field(..., description="The skin condition name")
    location: str = Field(..., description="Location of the condition on the face")
    description: str = Field(..., description="Detailed description of the condition")
    confidence: int = Field(..., ge=0, le=100, description="Confidence score between 0-100")

class FaceAnalysisResponse(BaseModel):
    quality_issues: List[Literal["blurry", "unclear", "others", "side_face"]] = Field(default_factory=list)
    gender: Optional[Literal["male", "female"]] = Field(None, description="Gender classification")
    ethnicity: Optional[Literal["ethnicity_northern", "ethnicity_southern", "ethnicity_north_eastern", "ethnicity_others"]] = Field(None, description="Ethnicity classification")
    age_group: Optional[Literal["15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80"]] = Field(None, description="Age group classification")
    skin_conditions: List[SkinCondition] = Field(default_factory=list, description="List of detected skin conditions")
```

### 2. Validation and Retry Function

The `validate_and_retry_response()` function:
- Attempts validation up to `MAX_RETRIES + 1` times (default: 4 total attempts)
- Cleans response text (removes markdown formatting)
- Validates JSON parsing
- Validates against Pydantic schema
- Manages prompt modifications for retries
- Returns structured success/failure information

### 3. Smart Prompt Management

**Problem Solved**: After each retry, the function was appending new instructions to the prompt, causing it to grow longer with each attempt.

**Solution**: The function now:
1. Stores the original instruction at the start
2. Resets to the original instruction before each retry
3. Adds specific error guidance for the current attempt
4. Prevents prompt bloat and maintains clarity

### 4. Configuration

Add to your `.env` file:
```bash
# Maximum number of retries for validation failures (default: 3)
MAX_RETRIES=3
```

## Usage Examples

### Sequential Processing with Validation

```python
# The validation happens automatically in the sequential processing
results = annotate_images(image_paths, use_batch=False)

# Check for manual review requirements
for img_name, result in results.items():
    if result.get("requires_manual_review", False):
        print(f"Manual review needed for {img_name}: {result['manual_review_reason']}")
```

### Batch Processing with Validation

Batch processing results are now also validated:
- Valid responses are stored with validated structure
- Invalid responses are flagged with validation warnings
- JSON decode failures are marked for manual review

## Error Handling

### Types of Errors Handled

1. **JSON Decode Errors**: When GenAI returns non-JSON or malformed JSON
2. **Validation Errors**: When JSON doesn't match the expected schema
3. **Generation Errors**: When the API call itself fails
4. **Max Retries Exceeded**: When all retry attempts are exhausted

### Manual Review Cases

Images are flagged for manual review when:
- All retry attempts fail due to validation errors
- JSON parsing consistently fails
- API generation errors persist

### Error Response Structure

```python
{
    "error": "validation_failed",
    "validation_errors": "detailed_error_message",
    "raw_response": "original_genai_response",
    "requires_manual_review": True,
    "manual_review_reason": "Validation failed after 4 attempts"
}
```

## Processing Summary

The script now provides detailed processing statistics:

```
=== SEQUENTIAL PROCESSING SUMMARY ===
Total images processed: 100
Successfully processed: 95
Failed with errors: 5
Require manual review: 3

⚠️  MANUAL REVIEW REQUIRED FOR 3 IMAGES:
   - image1.jpg: Validation failed after 4 attempts
   - image2.jpg: JSON decode failed: Expecting ',' delimiter
   - image3.jpg: Generation failed after 4 attempts
   Please check these images and try uploading them again.
====================================
```

## Backward Compatibility

The `convert_to_labelme()` function handles both:
- **Old format**: `{"classification_text": "json_string"}`
- **New format**: Direct validated JSON structure
- **Error cases**: Manual review flags and error details

## Testing

Run the validation test:
```bash
cd alaap_gemini_code
python test_validation.py
```

This tests:
- Valid response validation
- Invalid response detection
- Missing field handling
- Wrong data type detection
- Individual model validation

## Benefits

1. **Reliability**: Ensures consistent response format
2. **Error Recovery**: Automatic retry with improved prompts
3. **Monitoring**: Clear visibility into processing success/failure
4. **Quality Control**: Manual review flagging for problematic cases
5. **Maintainability**: Structured error handling and logging
6. **Performance**: Efficient prompt management without bloat

## Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `MAX_RETRIES` | Maximum retry attempts for validation failures | 3 |
| `USE_EXAMPLES_AND_CACHING` | Enable example images and caching | False |

## Troubleshooting

### High Manual Review Rate
- Check image quality (blurry, unclear images)
- Verify API key and connectivity
- Review prompt clarity and instructions

### Validation Errors
- Check if GenAI model is returning expected format
- Verify Pydantic model matches expected response structure
- Review error logs for specific validation issues

### Performance Issues
- Monitor retry frequency
- Consider adjusting MAX_RETRIES based on success rate
- Check network connectivity for API timeouts
