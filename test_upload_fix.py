#!/usr/bin/env python3
"""
Quick test to verify the upload fix works
"""

import os
import tempfile
from pathlib import Path
from google import genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()

def test_upload_method():
    """Test the upload method with a temporary file"""
    
    # Initialize client
    client = genai.Client()
    
    # Create a dummy image file (1x1 pixel PNG)
    # This is a minimal valid PNG file in bytes
    png_bytes = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    print("Testing upload method with temporary file approach...")
    
    temp_file_path = None
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(png_bytes)
            temp_file_path = temp_file.name
        
        print(f"Created temporary file: {temp_file_path}")
        
        # Test upload using positional argument (no 'file=' keyword)
        uploaded_img = client.files.upload(temp_file_path)
        print(f"✅ Upload successful! File URI: {uploaded_img.uri}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False
        
    finally:
        # Clean up
        if temp_file_path and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"Cleaned up temporary file: {temp_file_path}")

def test_original_method():
    """Test the original method with file path"""
    
    # Check if we have a sample image
    sample_image = "/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1ao0v53_p3ybp8qoawhc1.png"
    
    if not Path(sample_image).exists():
        print("⚠️ Sample image not found, skipping original method test")
        return True
    
    client = genai.Client()
    
    try:
        print(f"Testing original upload method with: {sample_image}")
        uploaded_img = client.files.upload(file=sample_image)
        print(f"✅ Original method successful! File URI: {uploaded_img.uri}")
        return True
        
    except Exception as e:
        print(f"❌ Original method failed: {e}")
        return False

if __name__ == "__main__":
    print("Google GenAI Upload Method Test")
    print("=" * 40)
    
    # Check API key
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY not set in environment")
        exit(1)
    
    print("✅ API key found")
    
    # Test both methods
    print("\n1. Testing temporary file approach:")
    temp_success = test_upload_method()
    
    print("\n2. Testing original file path approach:")
    original_success = test_original_method()
    
    print(f"\n{'='*40}")
    print("Test Results:")
    print(f"Temporary file method: {'✅ PASS' if temp_success else '❌ FAIL'}")
    print(f"Original file method: {'✅ PASS' if original_success else '❌ FAIL'}")
    
    if temp_success:
        print("\n🎉 The API fix should work!")
    else:
        print("\n⚠️ The API fix needs more work")
