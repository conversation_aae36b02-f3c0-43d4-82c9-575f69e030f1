#!/usr/bin/env python3
"""
Test script for the Face Analysis API
"""

import requests
import json
import sys
from pathlib import Path

API_BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_single_image_analysis(image_path):
    """Test single image analysis"""
    print(f"\nTesting single image analysis with: {image_path}")
    
    if not Path(image_path).exists():
        print(f"Image file not found: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}

            response = requests.post(f"{API_BASE_URL}/analyze", files=files)
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        return response.status_code == 200 and result.get('success', False)
    
    except Exception as e:
        print(f"Single image analysis failed: {e}")
        return False

def test_multiple_images_analysis(image_paths):
    """Test multiple images analysis"""
    print(f"\nTesting multiple images analysis with: {image_paths}")
    
    # Check if all files exist
    for path in image_paths:
        if not Path(path).exists():
            print(f"Image file not found: {path}")
            return False
    
    try:
        files = []
        for path in image_paths:
            files.append(('files', open(path, 'rb')))
        
        response = requests.post(f"{API_BASE_URL}/analyze-multiple", files=files)
        
        # Close all file handles
        for _, f in files:
            f.close()
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        return response.status_code == 200
    
    except Exception as e:
        print(f"Multiple images analysis failed: {e}")
        return False

def test_file_size_limit():
    """Test file size limit"""
    print(f"\nTesting file size limit...")
    
    # Create a dummy large file (simulate)
    print("Note: File size limit test requires a file larger than configured limit")
    print("This test is skipped in automated testing")
    return True

def test_invalid_file_format():
    """Test invalid file format handling"""
    print(f"\nTesting invalid file format...")
    
    try:
        # Create a dummy text file
        dummy_content = b"This is not an image file"
        files = {'file': ('test.txt', dummy_content, 'text/plain')}
        response = requests.post(f"{API_BASE_URL}/analyze", files=files)
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        # Should return 400 for invalid file format
        return response.status_code == 400
    
    except Exception as e:
        print(f"Invalid file format test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Face Analysis API Test Suite")
    print("=" * 40)
    
    # Test health check
    if not test_health_check():
        print("❌ Health check failed. Make sure the API server is running.")
        print("Start the server with: python gemini_api.py")
        sys.exit(1)
    else:
        print("✅ Health check passed")
    
    # Test with sample images (you need to provide actual image paths)
    sample_images = [
        "/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1ao0v53_p3ybp8qoawhc1.png",
        "/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1asmim9_dp4mfq1541jc1_face1.png",
    ]
    
    # Test single image analysis
    if sample_images and Path(sample_images[0]).exists():
        if test_single_image_analysis(sample_images[0]):
            print("✅ Single image analysis passed")
        else:
            print("❌ Single image analysis failed")
    else:
        print("⚠️  Skipping single image test - no sample image found")
    
    # Test multiple images analysis
    # existing_images = [img for img in sample_images if Path(img).exists()]
    # if len(existing_images) >= 2:
    #     if test_multiple_images_analysis(existing_images[:2]):
    #         print("✅ Multiple images analysis passed")
    #     else:
    #         print("❌ Multiple images analysis failed")
    # else:
    #     print("⚠️  Skipping multiple images test - need at least 2 sample images")
    
    # # Test file size limit
    # if test_file_size_limit():
    #     print("✅ File size limit test passed")
    # else:
    #     print("❌ File size limit test failed")
    
    # # Test invalid file format
    # if test_invalid_file_format():
    #     print("✅ Invalid file format test passed")
    # else:
    #     print("❌ Invalid file format test failed")
    
    print("\nTest suite completed!")
    print("\n📋 Manual testing with curl:")
    print("# Health check")
    print("curl http://localhost:8000/health")
    print("\n# Single image analysis")
    print("curl -X POST \"http://localhost:8000/analyze\" -F \"file=@/path/to/image.jpg\"")
    print("\n# Multiple images analysis")
    print("curl -X POST \"http://localhost:8000/analyze-multiple\" -F \"files=@image1.jpg\" -F \"files=@image2.jpg\"")

if __name__ == "__main__":
    main()
