# Face Analysis API

FastAPI-based REST API for AI-powered facial analysis and skin condition detection using Google Gemini.

## Features

- **Zero-Copy Processing**: Direct byte processing with no temporary files or disk I/O
- **Magic Bytes Validation**: Fast image format validation without PIL overhead
- **Configurable File Size Limits**: Maximum file size configurable via environment variables
- **Pydantic Validation**: Strict response validation with automatic retry mechanism
- **Comprehensive Analysis**: Detects demographics, skin conditions, and quality issues
- **Batch Processing**: Support for multiple image analysis
- **Production Optimized**: Minimal memory footprint and computational overhead
- **RESTful API**: Easy integration with curl, Postman, or any HTTP client

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Ensure your `.env` file has the required configuration:

```bash
# Google Generative AI API Key
GOOGLE_API_KEY=your_actual_api_key_here

# Retry Configuration
MAX_RETRIES=3

# File Upload Configuration (in MB)
MAX_FILE_SIZE_MB=20
```

### 3. Run the Server

```bash
python gemini_api.py
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Health Check

```bash
curl http://localhost:8000/health
```

**Response:**
```json
{
  "status": "healthy",
  "message": "API is healthy and ready to process images",
  "version": "1.0.0",
  "max_file_size_mb": 20
}
```

### Single Image Analysis

```bash
curl -X POST "http://localhost:8000/analyze" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@path/to/your/image.jpg"
```

**Response:**
```json
{
  "success": true,
  "message": "Image analyzed successfully",
  "data": {
    "quality_issues": [],
    "gender": "female",
    "ethnicity": "ethnicity_northern",
    "age_group": "25-30",
    "skin_conditions": [
      {
        "condition": "slight_wrinkles",
        "location": "around eyes",
        "description": "Fine lines visible around the eye area",
        "confidence": 75
      }
    ]
  }
}
```

### Multiple Images Analysis

```bash
curl -X POST "http://localhost:8000/analyze-multiple" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@image1.jpg" \
  -F "files=@image2.jpg" \
  -F "files=@image3.jpg"
```

**Response:**
```json
{
  "message": "Processed 3 images",
  "results": {
    "image1.jpg": {
      "success": true,
      "data": { ... }
    },
    "image2.jpg": {
      "success": true,
      "data": { ... }
    },
    "image3.jpg": {
      "success": false,
      "error": "Validation failed after 4 attempts"
    }
  }
}
```

## Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `GOOGLE_API_KEY` | Google Generative AI API key | Required |
| `MAX_RETRIES` | Maximum retry attempts for validation | 3 |
| `MAX_FILE_SIZE_MB` | Maximum file size in MB | 20 |

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff)
- WebP (.webp)

## File Size Limits

- **Default**: 20 MB per file
- **Configurable**: Set `MAX_FILE_SIZE_MB` in `.env`
- **Batch limit**: Maximum 5 files per batch request

## Analysis Categories

### Demographics
- **Gender**: male, female
- **Age Groups**: 15-20, 20-25, 25-30, 30-35, 35-40, 40-45, 45-50, 50-55, 55-60, 60-65, 65-70, 70-75, 75-80
- **Ethnicity**: ethnicity_northern, ethnicity_southern, ethnicity_north_eastern, ethnicity_others

### Quality Issues
- blurry: Image is blurry or out of focus
- unclear: Image quality is poor or unclear
- others: Not a human face (object, animal, etc.)
- side_face: Side profile instead of frontal view

### Skin Conditions
- excessive_oiliness, excessive_wrinkles, slight_wrinkles
- less_acne, excessive_acne, scar_due_to_acne, pimple
- hyper_pigmentation, uneven_pigmentation, spots
- small_skin_pores, big_skin_pores
- under_eye_bag, droopy_eyelid, redness, finelines
- eczema, psoriasis, rosacea

## Error Handling

### HTTP Status Codes

- `200`: Success
- `400`: Bad Request (invalid file format, missing file)
- `413`: Request Entity Too Large (file size exceeded)
- `422`: Unprocessable Entity (validation errors)
- `500`: Internal Server Error

### Error Response Format

```json
{
  "success": false,
  "message": "Image analysis failed",
  "error_details": {
    "error_type": "validation_failed_max_retries",
    "reason": "Validation failed after 4 attempts",
    "suggestion": "Please ensure the image is clear, well-lit, and shows a frontal view of a human face."
  }
}
```

## Testing

### Automated Tests

```bash
python test_api.py
```

### Manual Testing with Postman

1. **Health Check**
   - Method: GET
   - URL: `http://localhost:8000/health`

2. **Single Image Analysis**
   - Method: POST
   - URL: `http://localhost:8000/analyze`
   - Body: form-data
   - Key: `file` (File type)
   - Value: Select your image file

3. **Multiple Images Analysis**
   - Method: POST
   - URL: `http://localhost:8000/analyze-multiple`
   - Body: form-data
   - Key: `files` (File type, multiple files)
   - Value: Select multiple image files

### Testing with curl

```bash
# Test with a specific image
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@/home/<USER>/work/projects/Bespoke-beautytech/Annotation/annotation_data/top_few/1ao0v53_p3ybp8qoawhc1.png"

# Test file size limit (should fail for files > 20MB)
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@large_image.jpg"

# Test invalid file format (should fail)
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@document.txt"
```

## Performance Features

- **Zero File I/O**: No temporary files, direct memory-to-API processing
- **Magic Bytes Validation**: Fast format validation without image library overhead
- **Retry Mechanism**: Automatic retry with exponential backoff
- **Memory Efficient**: Direct BytesIO operations with minimal memory allocation
- **CPU Optimized**: No image decoding/encoding operations
- **Concurrent Processing**: FastAPI's async support for multiple requests
- **Production Ready**: Optimized for high-throughput scenarios

## Interactive Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Production Considerations

1. **CORS Configuration**: Update CORS origins for production
2. **Rate Limiting**: Implement rate limiting for API endpoints
3. **Authentication**: Add API key authentication
4. **Logging**: Configure structured logging
5. **Monitoring**: Add health checks and metrics
6. **Load Balancing**: Use multiple instances behind a load balancer

## Architecture

```
Client Request → FastAPI → Image Validation → Google Gemini AI → Pydantic Validation → Response
```

### Key Components

1. **FastAPI Application**: Handles HTTP requests and responses
2. **Image Processing**: PIL-based validation without temporary files
3. **Google Gemini Integration**: Direct API calls with retry logic
4. **Pydantic Validation**: Strict response schema validation
5. **Error Handling**: Comprehensive error management and logging

## Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   Error: GOOGLE_API_KEY environment variable is not set!
   Solution: Set your Google API key in .env file
   ```

2. **File Size Too Large**
   ```
   Error: File size exceeds maximum allowed size
   Solution: Reduce image size or increase MAX_FILE_SIZE_MB
   ```

3. **Invalid Image Format**
   ```
   Error: Invalid image file
   Solution: Use supported formats (jpg, png, bmp, tiff, webp)
   ```

4. **Validation Failures**
   ```
   Error: Validation failed after multiple retries
   Solution: Check image quality, ensure clear frontal face view
   ```

### Debug Mode

Run with debug logging:
```bash
uvicorn gemini_api:app --host 0.0.0.0 --port 8000 --log-level debug
```
